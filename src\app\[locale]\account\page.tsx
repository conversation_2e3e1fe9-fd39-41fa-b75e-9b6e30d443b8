'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import { createClient } from '@/lib/supabase/client';
import type { Database } from '@/lib/supabase/database.types';
import { formatCurrency } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { User, Package, Settings, LogOut, MapPin, ChevronRight, Gift, IdCard, Trophy, Star, Target, Percent, BarChart3, DollarSign } from 'lucide-react';
import { toast } from 'sonner';
import type { User as SupabaseUser } from '@supabase/supabase-js';
import { useAddresses } from '@/hooks/use-addresses';
import AddressForm from '@/components/account/address-form';
import AddressList from '@/components/account/address-list';
import GiftHistory from '@/components/account/gift-history';

export default function AccountPage() {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);

  // Profile form state
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    phone: ''
  });

  type OrderSummary = Pick<Database['public']['Tables']['orders']['Row'], 'id' | 'order_number' | 'created_at' | 'status' | 'total_amount' | 'tracking_number'>;
  const [orders, setOrders] = useState<OrderSummary[]>([]);
  type UserAddress = Database['public']['Tables']['user_addresses']['Row'];

  // Address management
  const {
    addresses,
    loading: addressesLoading,
    createAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress
  } = useAddresses(user);

  const [showAddressForm, setShowAddressForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null);
  type LoyaltyInfo = {
    currentLevel: number;
    currentLevelName: string;
    totalPoints: number;
    lifetimeSpend: number;
    pointsToNext: number;
    nextLevelName: string | null;
    discountPercent: number;
    pointsMultiplier: number;
    pointsToNextGift: number;
    nextGiftThreshold: number | null;
    pointsPerCHF: number;
    // New league information
    league: number;
    leagueName: string;
    leagueDiscountPercent: number;
  };
  const [loyalty, setLoyalty] = useState<LoyaltyInfo | null>(null);
  const [loyaltyLoading, setLoyaltyLoading] = useState(true);
  const [ordersLoading, setOrdersLoading] = useState(true);
  const router = useRouter();
  // Persist supabase client instance across renders to avoid effect loops
  const supabaseRef = useRef(createClient());
  const supabase = supabaseRef.current;
  const t = useTranslations('account');
  const tNav = useTranslations('navigation');
  const tCart = useTranslations('cart');
  const locale = useLocale();

  // Check current user once and subscribe to future auth state changes
  useEffect(() => {
    const checkCurrentUser = async () => {
      try {
        const { data, error } = await supabase.auth.getUser();
        if (error) {
          console.error('Error fetching current user:', error.message);
        }
        const current = data?.user ?? null;
        setUser(current);
        if (!current) {
          router.push(`/${locale}/login`);
        }
      } catch (err) {
        console.error('Unexpected error getting user:', err);
      } finally {
        setLoading(false);
      }
    };

    checkCurrentUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, locale]);

  // Load loyalty + orders when user is available
  useEffect(() => {
    if (!user) return;

    // Initialize profile data
    setProfileData({
      firstName: user.user_metadata?.first_name || '',
      lastName: user.user_metadata?.last_name || '',
      phone: user.user_metadata?.phone || ''
    });

    const loadOrders = async () => {
      try {
        setOrdersLoading(true);
        const { data, error } = await supabase
          .from('orders')
          .select('id, order_number, created_at, status, total_amount, tracking_number')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Error fetching user orders:', error);
        } else {
          setOrders(data || []);
        }
      } catch (err) {
        console.error('Unexpected error loading orders:', err);
      } finally {
        setOrdersLoading(false);
      }
    };

    const loadLoyalty = async () => {
      try {
        setLoyaltyLoading(true);
        const res = await fetch('/api/account/loyalty');
        if (!res.ok) throw new Error('Failed to fetch loyalty');
        const data: LoyaltyInfo = await res.json();
        setLoyalty(data);
      } catch (err) {
        console.error('Error fetching loyalty:', err);
      } finally {
        setLoyaltyLoading(false);
      }
    };

    loadOrders();
    loadLoyalty();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  // Address management handlers
  const handleAddAddress = () => {
    setEditingAddress(null);
    setShowAddressForm(true);
  };

  const handleEditAddress = (address: UserAddress) => {
    setEditingAddress(address);
    setShowAddressForm(true);
  };

  const handleAddressSubmit = async (data: Omit<Database['public']['Tables']['user_addresses']['Insert'], 'user_id'>) => {
    try {
      if (editingAddress) {
        await updateAddress(editingAddress.id, data);
      } else {
        await createAddress(data);
      }
      setShowAddressForm(false);
      setEditingAddress(null);
    } catch (error) {
      // Error handling is done in the hook
      throw error;
    }
  };

  const handleCancelAddressForm = () => {
    setShowAddressForm(false);
    setEditingAddress(null);
  };

  const handleDeleteAddress = async (id: string) => {
    await deleteAddress(id);
  };

  const handleSetDefaultAddress = async (id: string, type: 'billing' | 'shipping') => {
    await setDefaultAddress(id, type);
  };

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push(`/${locale}`);
  };

  const handleUpdateProfile = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setUpdating(true);

    try {
      // Update user metadata in Supabase Auth
      const { error: authError } = await supabase.auth.updateUser({
        data: {
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          phone: profileData.phone
        }
      });

      if (authError) {
        throw authError;
      }

      // Update user profile in database
      const { error: dbError } = await supabase
        .from('users')
        .update({
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          phone: profileData.phone,
          updated_at: new Date().toISOString()
        })
        .eq('id', user!.id);

      if (dbError) {
        throw dbError;
      }

      toast.success(t('profile.success'));
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error(t('profile.error'));
    } finally {
      setUpdating(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (newPassword !== confirmPassword) {
      toast.error('Passwörter stimmen nicht überein');
      return;
    }
    if (newPassword.length < 6) {
      toast.error('Passwort muss mindestens 6 Zeichen lang sein');
      return;
    }

    setChangingPassword(true);
    try {
      const { error } = await supabase.auth.updateUser({ password: newPassword });
      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Passwort erfolgreich aktualisiert');
        setNewPassword('');
        setConfirmPassword('');
      }
    } catch {
      toast.error('Fehler beim Aktualisieren des Passworts');
    } finally {
      setChangingPassword(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-muted rounded w-1/4"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">{t('title')}</h1>
            <p className="text-muted-foreground">{t('description')}</p>
          </div>
          <Button variant="outline" onClick={handleSignOut}>
            <LogOut className="mr-2 h-4 w-4" />
            {tNav('logout')}
          </Button>
        </div>

        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="profile">
              <User className="mr-2 h-4 w-4" />
              {t('profile.title')}
            </TabsTrigger>
            <TabsTrigger value="personal-data">
              <IdCard className="mr-2 h-4 w-4" />
              {t('personalData.title')}
            </TabsTrigger>
            <TabsTrigger value="orders">
              <Package className="mr-2 h-4 w-4" />
              {t('orders.title')}
            </TabsTrigger>
            <TabsTrigger value="gifts">
              <Gift className="mr-2 h-4 w-4" />
              {t('gifts.title')}
            </TabsTrigger>
            <TabsTrigger value="addresses">
              <MapPin className="mr-2 h-4 w-4" />
              {t('addresses.title')}
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="mr-2 h-4 w-4" />
              {t('security.title')}
            </TabsTrigger>
          </TabsList>

          {/* Profile Tab */}
          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>{t('profile.title')}</CardTitle>
              </CardHeader>
              <CardContent>
                {/* Gamification section */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold mb-6 flex items-center">
                    <Trophy className="mr-2 h-5 w-5 text-yellow-500" /> Sistema di Gamification
                  </h3>
                  {loyaltyLoading ? (
                    <div className="space-y-4">
                      <div className="h-32 bg-muted rounded-lg animate-pulse" />
                      <div className="h-20 bg-muted rounded-lg animate-pulse" />
                    </div>
                  ) : loyalty ? (
                    <div className="space-y-6">
                      {/* Main League Card */}
                      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-6 border shadow-sm">
                        <div className="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent" />
                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="text-3xl">
                                {loyalty.league === 1 && '🥉'}
                                {loyalty.league === 2 && '🥈'}
                                {loyalty.league === 3 && '🥇'}
                                {loyalty.league === 4 && '💎'}
                                {loyalty.league === 5 && '💠'}
                              </div>
                              <div>
                                <h4 className="text-xl font-bold text-gray-800">{loyalty.leagueName}</h4>
                                <p className="text-gray-600 text-sm">Lega {loyalty.league}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-gray-600 text-sm">Sconto attuale</p>
                              <p className="text-2xl font-bold text-emerald-600">{loyalty.leagueDiscountPercent}%</p>
                            </div>
                          </div>

                          {/* Level Progress */}
                          <div className="mb-4">
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium text-gray-700">{loyalty.currentLevelName}</span>
                              <span className="text-sm text-gray-600">
                                {loyalty.nextLevelName ? `Prossimo: ${loyalty.nextLevelName}` : 'Livello massimo'}
                              </span>
                            </div>

                            {/* Progress Bar */}
                            {loyalty.nextLevelName && (
                              <div className="relative">
                                <div className="w-full bg-gray-200 rounded-full h-3">
                                  <div
                                    className="bg-gradient-to-r from-emerald-400 to-teal-500 h-3 rounded-full transition-all duration-500 ease-out"
                                    style={{
                                      width: `${loyalty.pointsToNext > 0 ? Math.max(5, Math.min(95, (((loyalty.totalPoints + loyalty.pointsToNext) - loyalty.pointsToNext) / (loyalty.totalPoints + loyalty.pointsToNext)) * 100)) : 100}%`
                                    }}
                                  />
                                </div>
                                <div className="flex justify-between text-xs text-gray-600 mt-1">
                                  <span>{loyalty.totalPoints} punti</span>
                                  <span>{loyalty.pointsToNext} punti mancanti</span>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Gift Info */}
                          <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 border border-gray-200">
                            <div className="flex items-center gap-2 mb-2">
                              <Gift className="h-4 w-4 text-amber-600" />
                              <span className="text-sm font-medium text-gray-700">Sistema Regali</span>
                            </div>
                            <div className="text-xs text-gray-600 space-y-1">
                              <p>🎁 <strong>Regali di livello:</strong> Ricevi un regalo ad ogni nuovo livello raggiunto</p>
                              <p>🏆 <strong>Regali di lega:</strong> Ricevi un regalo speciale quando avanzi di lega (ogni 10 livelli)</p>
                              <p className="text-amber-700">💡 I regali di lega hanno priorità sui regali di livello</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Stats Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="group relative overflow-hidden rounded-lg border bg-gradient-to-br from-amber-50 to-yellow-50 p-4 text-center transition-all hover:shadow-md">
                          <div className="absolute inset-0 bg-gradient-to-br from-amber-100/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="relative z-10">
                            <Trophy className="h-6 w-6 text-amber-600 mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">{t('loyalty.currentLevel')}</p>
                            <p className="text-xl font-bold text-amber-700">{loyalty.currentLevelName}</p>
                            <p className="text-xs text-muted-foreground">Livello {loyalty.currentLevel}</p>
                          </div>
                        </div>

                        <div className="group relative overflow-hidden rounded-lg border bg-gradient-to-br from-blue-50 to-sky-50 p-4 text-center transition-all hover:shadow-md">
                          <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="relative z-10">
                            <Star className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">{t('loyalty.totalPoints')}</p>
                            <p className="text-xl font-bold text-blue-700">{loyalty.totalPoints}</p>
                            <p className="text-xs text-muted-foreground">punti totali</p>
                          </div>
                        </div>

                        <div className="group relative overflow-hidden rounded-lg border bg-gradient-to-br from-emerald-50 to-teal-50 p-4 text-center transition-all hover:shadow-md">
                          <div className="absolute inset-0 bg-gradient-to-br from-emerald-100/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="relative z-10">
                            <Target className="h-6 w-6 text-emerald-600 mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">{t('loyalty.pointsToNext')}</p>
                            <p className="text-xl font-bold text-emerald-700">{loyalty.pointsToNext}</p>
                            <p className="text-xs text-muted-foreground">
                              {loyalty.nextLevelName ? `per ${loyalty.nextLevelName}` : 'livello massimo'}
                            </p>
                          </div>
                        </div>

                        <div className="group relative overflow-hidden rounded-lg border bg-gradient-to-br from-slate-50 to-gray-50 p-4 text-center transition-all hover:shadow-md">
                          <div className="absolute inset-0 bg-gradient-to-br from-slate-100/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="relative z-10">
                            <Percent className="h-6 w-6 text-slate-600 mx-auto mb-2" />
                            <p className="text-sm text-muted-foreground">Sconto attuale</p>
                            <p className="text-xl font-bold text-slate-700">{loyalty.leagueDiscountPercent}%</p>
                            <p className="text-xs text-muted-foreground">sui tuoi acquisti</p>
                          </div>
                        </div>
                      </div>

                      {/* Lifetime Stats */}
                      <div className="rounded-lg border bg-gradient-to-r from-slate-50 to-gray-50 p-6 shadow-sm">
                        <h4 className="font-semibold mb-4 flex items-center gap-2">
                          <BarChart3 className="h-5 w-5 text-slate-600" />
                          {t('loyalty.lifetimeStats')}
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div className="flex items-center gap-4">
                            <div className="rounded-full bg-green-100 p-3">
                              <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">{t('loyalty.totalSpent')}</p>
                              <p className="text-2xl font-bold text-green-700">{formatCurrency(loyalty.lifetimeSpend)}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="rounded-full bg-blue-100 p-3">
                              <Star className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">{t('loyalty.pointsEarned')}</p>
                              <p className="text-2xl font-bold text-blue-700">{loyalty.totalPoints} punti</p>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* Next Gift Section */}
                      {loyalty.pointsToNextGift > 0 && (
                        <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 p-6 border shadow-sm">
                          <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-transparent" />
                          <div className="relative z-10">
                            <div className="flex items-center gap-3 mb-4">
                              <div className="rounded-full bg-emerald-100 p-2">
                                <Gift className="h-6 w-6 text-emerald-600" />
                              </div>
                              <div>
                                <h4 className="text-lg font-bold text-gray-800">Prossimo Regalo</h4>
                                <p className="text-emerald-700 text-sm">Continua a guadagnare punti!</p>
                              </div>
                            </div>

                            <div className="bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-emerald-200">
                              <div className="flex items-center justify-between mb-3">
                                <span className="text-sm font-medium text-gray-700">Progresso verso il regalo</span>
                                <span className="text-sm text-emerald-700">
                                  {loyalty.pointsToNextGift} punti mancanti
                                </span>
                              </div>

                              {/* Gift Progress Bar */}
                              <div className="relative">
                                <div className="w-full bg-emerald-200 rounded-full h-2">
                                  <div
                                    className="bg-gradient-to-r from-emerald-400 to-teal-500 h-2 rounded-full transition-all duration-500 ease-out"
                                    style={{
                                      width: `${loyalty.nextGiftThreshold ? Math.max(5, Math.min(95, ((loyalty.nextGiftThreshold - loyalty.pointsToNextGift) / loyalty.nextGiftThreshold) * 100)) : 0}%`
                                    }}
                                  />
                                </div>
                              </div>

                              {loyalty.nextGiftThreshold && loyalty.pointsPerCHF && (
                                <p className="text-xs text-gray-600 mt-2 text-center">
                                  Spesa equivalente: {formatCurrency(loyalty.pointsToNextGift / loyalty.pointsPerCHF)}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">-</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Personal Data Tab */}
          <TabsContent value="personal-data">
            <Card>
              <CardHeader>
                <CardTitle>{t('personalData.title')}</CardTitle>
                <p className="text-muted-foreground">{t('personalData.description')}</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleUpdateProfile} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="email">{t('profile.email')}</Label>
                      <Input
                        id="email"
                        type="email"
                        value={user.email || ''}
                        disabled
                        className="bg-muted"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">{t('profile.phone')}</Label>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="+41 44 123 45 67"
                        value={profileData.phone}
                        onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">{t('profile.firstName')}</Label>
                      <Input
                        id="firstName"
                        placeholder="Max"
                        value={profileData.firstName}
                        onChange={(e) => setProfileData(prev => ({ ...prev, firstName: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">{t('profile.lastName')}</Label>
                      <Input
                        id="lastName"
                        placeholder="Mustermann"
                        value={profileData.lastName}
                        onChange={(e) => setProfileData(prev => ({ ...prev, lastName: e.target.value }))}
                      />
                    </div>
                  </div>
                  <Button type="submit" disabled={updating}>
                    {updating ? '...' : t('profile.save')}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Orders Tab */}
          <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>{t('orders.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              {ordersLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
                </div>
              ) : orders.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">{t('orders.orderNumber')}</th>
                        <th className="text-left py-3 px-4">{t('orders.date')}</th>
                        <th className="text-left py-3 px-4">{t('orders.status')}</th>
                        <th className="text-left py-3 px-4">{t('orders.trackingNumber')}</th>
                        <th className="text-left py-3 px-4">{t('orders.total')}</th>
                        <th className="w-4" />
                      </tr>
                    </thead>
                    <tbody>
                      {orders.map(order => (
                        <tr
                          key={order.id}
                          className="border-b hover:bg-muted/50 cursor-pointer"
                          onClick={() => router.push(`/${locale}/account/orders/${order.id}`)}
                        >
                          <td className="py-3 px-4 font-mono">#{order.order_number || order.id.slice(0, 8)}</td>
                          <td className="py-3 px-4">
                            {new Date(order.created_at).toLocaleDateString(locale === 'it' ? 'it-CH' : locale === 'fr' ? 'fr-CH' : 'de-CH')}
                          </td>
                          <td className="py-3 px-4">
                            <Badge className={`${getStatusColor(order.status)} w-fit`}>
                              {t(`orders.statuses.${order.status}`)}
                            </Badge>
                          </td>
                          <td className="py-3 px-4">
                            {order.tracking_number ? (
                              <span className="font-mono text-sm">{order.tracking_number}</span>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </td>
                          <td className="py-3 px-4 font-semibold">{formatCurrency(order.total_amount)}</td>
                           <td className="py-3 px-4 text-right">
                             <ChevronRight className="h-4 w-4 text-muted-foreground" />
                           </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12">
                  <Package className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">{t('orders.noOrders')}</h3>
                  <p className="text-muted-foreground mb-4">
                    {t('orders.noOrdersDescription')}
                  </p>
                  <Button asChild>
                    <a href={`/${locale}/shop`}>{tCart('continueShopping')}</a>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          </TabsContent>

          {/* Addresses Tab */}
          <TabsContent value="addresses">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
                <CardTitle>{t('addresses.title')}</CardTitle>
                {!showAddressForm && (
                  <Button onClick={handleAddAddress}>
                    {t('addresses.addNew')}
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {showAddressForm ? (
                  <AddressForm
                    address={editingAddress || undefined}
                    onSubmit={handleAddressSubmit}
                    onCancel={handleCancelAddressForm}
                    isLoading={addressesLoading}
                    mode={editingAddress ? 'edit' : 'add'}
                  />
                ) : (
                  <AddressList
                    addresses={addresses}
                    onEdit={handleEditAddress}
                    onDelete={handleDeleteAddress}
                    onSetDefault={handleSetDefaultAddress}
                    loading={addressesLoading}
                  />
                )}


              </CardContent>
            </Card>
          </TabsContent>

          {/* Gifts Tab */}
          <TabsContent value="gifts">
            <GiftHistory />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <div className="space-y-6">
              {/* Change Password */}
              <Card>
                <CardHeader>
                  <CardTitle>{t('security.changePassword')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleChangePassword} className="space-y-4 max-w-sm">
                    <div>
                      <Label htmlFor="newPassword">{t('security.newPassword')}</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="confirmPassword">{t('security.confirmPassword')}</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                      />
                    </div>
                    <Button type="submit" disabled={changingPassword}>
                      {changingPassword ? '...' : t('security.save')}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('security.notificationsTitle')}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('security.emailNotificationsTitle')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('security.emailNotificationsDesc')}
                      </p>
                    </div>
                    <Badge variant="secondary">{t('security.enabled')}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{t('security.marketingEmailsTitle')}</p>
                      <p className="text-sm text-muted-foreground">
                        {t('security.marketingEmailsDesc')}
                      </p>
                    </div>
                    <Badge variant="outline">{t('security.disabled')}</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{t('security.deleteAccountTitle')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    {t('security.deleteAccountDescription')}
                  </p>
                  <Button variant="destructive">{t('security.deleteAccount')}</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
